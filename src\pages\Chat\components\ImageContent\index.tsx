import type { ChatMessage } from '@/models/chat';
import { Image, ImagePreview } from '@douyinfe/semi-ui';
import { CSSProperties } from 'react';
import styles from './index.less';

const ImageContentRender: React.FC<{ message: ChatMessage; imageStyle?: CSSProperties }> = ({
  message,
  imageStyle,
}) => {
  // 识别生成的图片是错误的标识
  const errorImageFlag = 'AI:IMAGE:TASK:ERROR';

  // 判断是否是加载状态
  const isLoading = message?.status === 'loading' || message?.status === 'progress';

  const getContentAndFileList = () => {
    let content = '';
    const fileList: any[] = [];

    if (typeof message.content === 'string') {
      content = message.content;
    } else if (Array.isArray(message.content)) {
      message.content.forEach((item: any) => {
        if (item.type === 'text') {
          content = item?.text || '';
        } else if (item.type === 'image_url') {
          fileList.push({
            type: 'image',
            name: item?.image_url?.name || item?.text || '图片',
            url: item.image_url.url,
            alt: item?.image_url?.name || item?.text || '图片',
          });
        }
      });
    }

    return { content, fileList };
  };
  const { content, fileList } = getContentAndFileList();

  // 如果是加载状态且没有内容，不显示任何内容（由LoadingRender处理）
  if (isLoading && !fileList?.length && !content) {
    return null;
  }

  return (
    <div className={styles.imageContent}>
      {!fileList?.length && content && <p className={styles.noImage}>{content}</p>}
      {!fileList?.length && !content && !isLoading && (
        <p className={styles.noImage}>暂无图片生成</p>
      )}
      <ImagePreview className={styles.imagePreview}>
        {fileList?.length
          ? (fileList || []).map((item, index) => {
              return (
                <Image
                  key={index}
                  src={item?.url}
                  alt={`lamp${index + 1}`}
                  imgStyle={imageStyle || {}}
                  preview={!item?.name?.includes(errorImageFlag)}
                />
              );
            })
          : ''}
      </ImagePreview>
    </div>
  );
};
export default ImageContentRender;
