import type { Message } from '@douyinfe/semi-ui/lib/es/chat/interface';
import type { FileItem } from '@douyinfe/semi-ui/lib/es/upload';

export interface ChatInstanceRef {
  handleMessageSend: (
    content: string,
    attachment: FileItem[],
    messages?: Message[],
  ) => Promise<void>;
  clearMessages: () => void;
  getMessages: () => Message[];
  handleChatsChange: (messages: Message[]) => void;
  handleChatReconnect: (msg?: Message) => void;
}

// 图片流式数据结构
export interface ImgContentData {
  type: 'image_url';
  image_url: {
    url: string;
    name: string;
    size: string;
    width: number;
    height: number;
    type: 'file';
  };
}

// ppt详情信息
export interface PPTDetailType {
  createTime: string;
  updateTime: string;
  creator: string;
  updater: number;
  deleted: boolean;
  id: number;
  userId: number;
  userDesignId: number;
  taskId: number;
  title: number;
  thumbnail: number;
  pptUrl: number;
  pdfUrl: number;
  genStatus: number;
  chatId: number;
  chatResId: number;
}

export interface ChatProps {
  title?: string;
  placeholder?: string;
  smarPlaceholder?: string;
  routeId?: number;
  isTopSlot?: boolean;
  onlineQueryProps?: {
    visible: boolean; // 是否显示 默认为true
    disabled: boolean; // 是否联网查询
  };
  appCode?: string;
  className?: string;
  imgConfig?: any;
  customHandleSend?: (content: string, attachment: FileItem[]) => void;
  chatTopSlot?: React.ReactNode;
  showTemplateHeader?: boolean;
  // 是否显示知识库列表
  initialInputContent?: string;
  customChatFunction?: Record<string, (data: any) => void>;
  customHandleSendReading?: (content: string, attachment: FileItem[]) => void;
  vditorTitle?: string;
  childLevelCode?: string;
}

export type ParseStatus = 'pending' | 'success' | 'error' | '';

// Chat组件的 提问时的props
export interface QuestionsData {
  appCode?: string;
  content: string;
  attachments: FileItem[] | string;
  messages?: Message[];
  fromOffice?: boolean;
  selectedTool?: any;
  routeId?: number;
  isOnlineState?: boolean;
}

export interface Attachment {
  type: 'file' | 'image';
  name: string;
  url: string;
}

// Chat message type类型 reasoning -> 思考内容
export type ChatMsgType =
  | 'text'
  | 'reasoning'
  | 'image'
  | 'file'
  | 'ppt'
  | 'chat_json'
  | 'params_json';

export interface ChatMessageContent {
  type: ChatMsgType;
  text?: {
    content: string;
  };
  chatJson?: {
    content: string;
  };
  reasoning?: {
    content: string;
  };
  ppt?: PPTDetailType;
  file?: {
    url?: string;
    name?: string;
    size?: string;
    type?: string;
    fileTags?: string[];
  };
  url?: string;
  paramsJson?: any;
}

export interface MergedItem {
  collectionId: string;
  datasetId: string;
  hlQ: string | string[];
  hlSourceName: string;
  q: string[] | string;
  sourceId: string;
  sourceName: string;
  xquery: string;
}

export interface ChatListResult {
  id: any;
  content: Message['content'][];
  reasoningContent: string;
  role: string;
  like: boolean;
  dislike: boolean;
  quoteList?: MergedItem[];
  pptDetail?: any;
  contentJson?: Record<string, any>;
  paramsJson?: Record<string, any>;
  isReconnect?: boolean;
}

// 智慧工程计算书审核
export interface SmartFileStatus {
  status: boolean;
  uid: string;
  percent: number;
}

export const enum SmartCurrTabEnum {
  engin = 1,
  report_compare,
  report_valid,
  vision,
}

export interface AfterUploadResult {
  code: number;
  msg?: string;
  data?: any;
  uid?: string;
}
