import { ASSISTANT_ROLE, DEFAULT_ANSWER, USER_ROLE } from '@/config/chat';
import { getHistoryChatList } from '@/services/chat';
import { dispatchInUtils, getState } from '@/utils';
import type { Message } from '@douyinfe/semi-ui/lib/es/chat/interface';
import classNames from 'classnames';
import React, { useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';
import { useLocation, useNavigate, useParams, useSelector } from 'umi';
import AIChat from '../Chat';
import type { ChatInstanceRef, ChatListResult, ChatMessageContent, QuestionsData } from '../types';

import { ChatModelState } from '@/models/chat';
import VditorEditor from '@/pages/Chat/components/Vditor';
import { mergeByCollectionId } from '@/utils';
import styles from './index.less';
const ChatPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [isClosing, setIsClosing] = useState(true);
  const { chatId } = useParams<{ chatId: string }>();
  const appCode = searchParams.get('appCode') || '';
  const type = searchParams.get('type') || '';
  const chatRef = useRef<ChatInstanceRef>(null);
  const location = useLocation();
  const [isFromOffice, setIsFromOffice] = useState(true);
  //const [content, setContent] = useState('');
  const chatState = useSelector((state: { chat: ChatModelState }) => state.chat);
  const { chatMsg, isExpand, realTimeContent } = chatState;
  const [isShowVditor, setIsShowVditor] = useState(false);
  const [writeXTitle, setWriteXTitle] = useState('');
  const [writeXContent, setwriteXContent] = useState('');

  const writeContent = useSelector((state: { aiWrite: { content: '' } }) => state.aiWrite.content);

  useEffect(() => {
    if (writeContent.trim().indexOf('##') === 0) {
      let _tmps = writeContent.trim().split('\n');
      setWriteXTitle(_tmps[0].replace('##', '').trim());

      _tmps.splice(0, 1);
      setwriteXContent(_tmps.join('\n'));
    } else {
      setWriteXTitle('');
      setwriteXContent(writeContent);
    }
  }, [writeContent]);

  // 获取当前对话"assistant"的 content
  // useEffect(() => {
  //   setContent(''); // 组件加载时设置为空
  // }, [location.pathname]);

  const dooScrollToEditorBottom = () => {
    setTimeout(() => {
      let editDom = document.querySelector('#vditor .vditor-reset');
      if (editDom) {
        editDom.scrollTo({
          top: editDom.scrollHeight,
        });
      }
    }, 100);
  };

  useEffect(() => {
    try {
      // 检查 chatMsg 和 chatId 是否有效
      // if (!chatMsg || !chatId || !chatMsg[chatId]) {
      //   console.log('没有找到聊天记录或chatId无效');
      //   return;
      // }

      // // 获取消息数组，提供默认空数组防止undefined
      // const messages = chatMsg[chatId].messages || [];

      // // 检查是否为数组类型
      // if (!Array.isArray(messages)) {
      //   console.error('messages不是数组类型:', messages);
      //   return;
      // }

      // // 一次性查找最后一条助手消息
      // const assistantMessages = messages.filter((item) => item?.role === 'assistant');

      // // 检查是否找到助手消息
      // if (assistantMessages.length === 0) {
      //   console.log('没有找到助手消息');
      //   return;
      // }

      // // 获取最后一条助手消息的内容
      // const lastAssistantMessage = assistantMessages[assistantMessages.length - 1];
      // console.log('lastAssistantMessage', lastAssistantMessage);
      const content = realTimeContent;

      // 只有当内容存在时才设置状态
      if (content) {
        //setContent(content as string);
        dispatchInUtils({
          type: 'aiWrite/setContent',
          payload: {
            content,
          },
        });

        dooScrollToEditorBottom();
      }
    } catch (error) {
      console.error('处理聊天内容时出错:', error);
    }
  }, [chatId, realTimeContent]);
  const [initialContent, setInitialContent] = useState('');

  const navigate = useNavigate();

  // const [hints, setHints] = useState<string[]>([]);
  interface MsgType {
    type: 'text' | 'reasoning' | 'image_url' | 'file_url';
    text: string;
    reasoningContent?: string;
    image_url?: { url: string };
    file_url?: { url: string; [x: string]: any };
  }
  enum MessageType {
    text = 'text',
    reasoning = 'reasoning',
    image = 'image_url',
    file = 'file_url',
  }
  const getContent = (messages: ChatMessageContent[]) => {
    let reasoningContent = '';
    const content: Message['content'][] = [];
    messages.forEach((msg) => {
      const temp: MsgType = {
        type: MessageType[msg.type],
        text: '',
      };
      if (msg.type === 'text') {
        temp.text += msg?.text?.content || DEFAULT_ANSWER;
      } else if (msg.type === 'reasoning' && temp.reasoningContent) {
        temp.reasoningContent += msg?.reasoning?.content;
      }
      const isFile = msg.type === 'file';
      const { name, size, url, type } = msg.file || {};
      if (isFile && type === 'image') {
        temp.type = 'image_url';
        temp.image_url = {
          url: url || '',
        };
      }
      if (isFile && type === 'file') {
        temp.type = 'file_url';
        temp.file_url = {
          url: url || '',
          name: name || '',
          size: size || '',
          type: msg.type,
        };
      }
      content.push(temp as unknown as Message['content']);
    });
    return {
      content,
      reasoningContent,
    };
  };

  const dooHandleChatsChange = (chatList: Message[]) => {
    chatRef.current?.handleChatsChange(chatList);

    /*
    let lastContent = "" ;
    chatList && chatList.map(it=>{
      if(it.role != 'assistant') return ;
      if(!it.content || !it.content[0] || it.content[0].type != 'text') return ;
      lastContent = it.content[0].text ;
    });

    if(lastContent) {
      dispatchInUtils({
        type: 'aiWrite/setContent',
        payload: {
          content : lastContent,
        },
      });
    }
    */
  };

  const normalizeChatData = (messages: ChatMessageContent[]) => {
    let reasoningContent = '';
    const content: Message['content'][] = [];
    let pptDetail = {};
    let contentJson = {};
    messages.forEach((msg) => {
      const temp: MsgType = {
        type: MessageType[msg.type],
        text: '',
      };
      if (msg.type === 'text') {
        temp.text += msg?.text?.content;
      }
      if (msg.type === 'reasoning') {
        reasoningContent += msg?.reasoning?.content;
      }
      if (msg.type === 'ppt') {
        pptDetail = msg.ppt || {};
      }
      if (msg.type === 'chat_json') {
        contentJson = JSON.parse(msg?.chatJson?.content || '{}');
      }
      const isFile = msg.type === 'file';
      const { name, size, url, type, fileTags } = msg.file || {};
      if (isFile && type === 'image') {
        temp.type = 'image_url';
        temp.image_url = {
          url: url || '',
          name: name || '',
          size: size || '',
          type: msg.type,
        };
      }
      if (isFile && type === 'file') {
        temp.type = 'file_url';
        temp.file_url = {
          url: url || '',
          name: name || '',
          size: size || '',
          type: msg.type,
          fileTags: fileTags || [],
        };
      }
      content.push(temp as unknown as Message['content']);
    });
    return {
      content,
      pptDetail,
      contentJson,
      reasoningContent,
    };
  };

  const fetchHistoryList = () => {
    getHistoryChatList({
      appCode,
      chatId,
      offSet: 0,
      pageSize: 1024,
    })
      .then((res) => {
        const data = res?.data?.data?.list || [];

        const Role = {
          Human: USER_ROLE,
          AI: ASSISTANT_ROLE,
        };

        if (data?.length) {
          const chatList = data.map((item: any) => {
            const { content, pptDetail, reasoningContent, contentJson } = normalizeChatData(
              item.value,
            );

            const result: ChatListResult = {
              id: item.dataId,
              content,
              reasoningContent,
              role: Role[item.obj as 'Human' | 'AI'] || '',
              like:
                (typeof item.userGoodFeedback === 'string' && item.userGoodFeedback === 'yes') ||
                item.userGoodFeedback,
              dislike:
                (typeof item.userBadFeedback === 'string' && item.userBadFeedback === 'yes') ||
                item.userBadFeedback,
              ...(item.obj === 'AI' && { quoteList: mergeByCollectionId(item.quote || []) }),
            };
            if (appCode === 'ppt' && item.obj === 'AI') {
              result.pptDetail = pptDetail;
            }
            if (item.obj === 'AI' && contentJson) {
              result.contentJson = contentJson;
            }
            return result;
          });

          // 更新Redux状态中的消息数据
          dispatchInUtils({
            type: 'chat/saveMessage',
            payload: {
              [chatId]: {
                ...getState()?.chat.chatMsg![chatId],
                messages: chatList as unknown as Message[],
                pending: false,
              },
            },
          });

          // 通知聊天组件更新显示
          dooHandleChatsChange(chatList as unknown as Message[]);
        } else {
          // 如果没有历史数据，确保清空消息
          dispatchInUtils({
            type: 'chat/saveMessage',
            payload: {
              [chatId]: {
                ...getState()?.chat.chatMsg![chatId],
                messages: [],
                pending: false,
              },
            },
          });
          dooHandleChatsChange([]);
        }
      })
      .catch((error) => {
        console.error('Failed to fetch history list:', error);
        // 发生错误时，确保有基本的状态
        dispatchInUtils({
          type: 'chat/saveMessage',
          payload: {
            [chatId]: {
              ...getState()?.chat.chatMsg![chatId],
              messages: [],
              pending: false,
            },
          },
        });
        dooHandleChatsChange([]);
      });
  };

  useEffect(() => {
    // 新对话跳转
    if (chatId) {
      if (!appCode && location.state) {
        const { content, attachments, fromOffice } = (location.state as QuestionsData) || {};
        // 如果state中有fromOffice属性，则更新状态
        if (fromOffice !== undefined) {
          setIsFromOffice(fromOffice);
        }

        chatRef.current?.clearMessages();
        const customAttachments =
          typeof attachments === 'string' ? JSON.parse(attachments) : attachments;

        if (content || customAttachments?.length) {
          setInitialContent(content);
          chatRef.current?.handleMessageSend(content, customAttachments);
          const newParams = new URLSearchParams(searchParams);
          newParams.set('appCode', type || 'chat');
          newParams.delete('type');
          setSearchParams(newParams);
        }
        return;
      }
      // 检查当前chatId是否有缓存的消息
      const currentChatData = getState()?.chat.chatMsg![chatId];

      if (!currentChatData || !currentChatData.messages || currentChatData.messages.length === 0) {
        // 如果没有缓存数据或消息为空，初始化并获取历史数据
        dispatchInUtils({
          type: 'chat/saveMessage',
          payload: {
            [chatId]: {
              messages: [],
              pending: false,
            },
          },
        });

        // 管理缓存数量，但不删除当前正在查看的chatId
        const chatMsg = getState()?.chat.chatMsg;
        const ChatKeys = Object.keys(chatMsg);
        if (ChatKeys.length > 5) {
          // 找到最早的且不是当前chatId的记录进行删除
          const oldestChatId = ChatKeys.find((key) => key !== chatId);
          if (oldestChatId) {
            dispatchInUtils({
              type: 'chat/deleteChatId',
              payload: {
                chatId: oldestChatId,
              },
            });
          }
        }

        // 获取历史对话数据
        fetchHistoryList();
      } else {
        // 如果有缓存数据，直接使用，但同时也要检查是否需要更新
        dooHandleChatsChange(currentChatData.messages);

        // 对于某些应用类型，始终获取最新的历史对话数据以确保数据同步
        if (['image'].includes(appCode) || currentChatData.messages.length === 0) {
          fetchHistoryList();
        }
      }
    }

    let showvditor = searchParams.get('showvditor');
    if (showvditor == '1') {
      setTimeout(() => {
        dispatchInUtils({
          type: 'pageLayout/changePageMode',
          payload: 'doc',
        });
        dispatchInUtils({
          type: 'chat/setIsShowVditor',
          payload: true,
        });
        setIsClosing(false);
        setIsShowVditor(true);
      }, 1500);
    }
  }, [chatId, location.state]);

  useEffect(() => {
    if (isExpand) {
      setIsClosing(false);
      dispatchInUtils({
        type: 'chat/setIsExpand',
        payload: true,
      });
      dispatchInUtils({
        type: 'pageLayout/changePageMode',
        payload: 'doc',
      });
    }
  }, [isExpand]);
  const handleChange = (newValue: string) => {
    dispatchInUtils({
      type: 'aiWrite/setContent',
      payload: {
        content: newValue,
      },
    });
  };
  // 关闭页面
  const handleClose = () => {
    setIsClosing(true);
    dispatchInUtils({
      type: 'chat/setIsShowVditor',
      payload: false,
    });
    // 延长动画时间到 300ms
    setTimeout(() => {
      dispatchInUtils({
        type: 'pageLayout/changePageMode',
        payload: '',
      });
      dispatchInUtils({
        type: 'chat/setIsExpand',
        payload: false,
      });
    }, 300);
  };

  const handleSend = (content: string, attachment: []) => {
    dispatchInUtils({
      type: 'chat/setIsShowVditor',
      payload: true,
    });
    dispatchInUtils({
      type: 'chat/setIsExpand',
      payload: true,
    });
    setInitialContent(content);
  };

  useEffect(() => {
    return () => {
      handleClose();
    };
  }, [chatId, JSON.stringify(location.state)]);

  const LoadingIndicator = () => {
    const pathname = location.pathname;
    if (!pathname.includes('chat-read')) {
      return;
    }
    // 如果为空 返回
    if (!chatMsg[chatId]) {
      return;
    }

    // 获取消息数组，提供默认空数组防止undefined
    const messages = chatMsg[chatId].messages || [];

    // 检查是否为数组类型
    if (!Array.isArray(messages)) {
      console.error('messages不是数组类型:', messages);
      return;
    }

    // 一次性查找最后一条助手消息
    const assistantMessages = messages.filter((item) => item?.role === ASSISTANT_ROLE);
    // 取最后一条数据
    const lastMessage = assistantMessages.at(-1);
    let status = false;
    if (lastMessage) {
      // 使用最后一条消息
      status = lastMessage.status === 'incomplete';
    } else {
      return;
    }

    return (
      <>
        {status && (
          <div className={styles.loadingContainer}>
            <span className={styles.text}>AI 撰写中...</span>
          </div>
        )}
      </>
    );
  };

  return (
    <div className={classNames(styles.wrap, { [styles.hidePage]: isClosing })}>
      <div
        className={classNames(styles.chatContainer, {
          [styles.ptop]: !isClosing,
        })}
      >
        <AIChat
          ref={chatRef}
          appCode={appCode || type}
          showTemplateHeader={isFromOffice}
          customHandleSendReading={handleSend}
          vditorTitle={initialContent}
        />
      </div>

      {isShowVditor ? (
        <div
          className={classNames(styles.pdfContainer, {
            [styles.closing]: isClosing,
          })}
        >
          <VditorEditor
            value={writeXContent}
            onChange={handleChange}
            height={'95vh'}
            placeholder="请输入内容..."
            saveUrl="/api/content/save"
            saveMethod="POST"
            onClose={handleClose}
            title={writeXTitle}
          />
        </div>
      ) : (
        <></>
      )}
      <LoadingIndicator />
    </div>
  );
};

export default ChatPage;
